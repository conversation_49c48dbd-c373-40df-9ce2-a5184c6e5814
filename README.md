# Real-Time Transcription System

A real-time audio transcription system built with React, Node.js WebSocket server, and FastAPI with OpenAI Whisper.

## Architecture

- **Client**: React frontend with TypeScript for audio recording and real-time transcription display
- **Server**: Node.js WebSocket server with TypeScript for handling real-time communication
- **Whisper API**: FastAPI service with OpenAI Whisper for audio transcription

## Project Structure

```
real-time-transcription/
├── client/                         # React frontend (TS)
│   ├── public/
│   └── src/
│       ├── App.tsx
│       └── index.tsx
├── server/                         # Node.js WebSocket server (TS)
│   ├── src/
│   │   └── index.ts
│   └── tsconfig.json
├── whisper-api/                    # FastAPI + Whisper service (Python)
│   └── whisper_api.py
└── README.md
```

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- Python 3.8+
- npm or yarn

### Quick Start

#### Option 1: Using Development Scripts (Recommended)

**For Linux/macOS:**
```bash
chmod +x scripts/start-dev.sh
./scripts/start-dev.sh
```

**For Windows:**
```cmd
scripts\start-dev.bat
```

#### Option 2: Using npm scripts
```bash
# Install all dependencies
npm run install:all
npm run install:whisper

# Start all services in development mode
npm run dev
```

#### Option 3: Manual Setup

1. **Install client dependencies:**
   ```bash
   cd client
   npm install
   ```

2. **Install server dependencies:**
   ```bash
   cd server
   npm install
   ```

3. **Install Python dependencies:**
   ```bash
   cd whisper-api
   # Create virtual environment (recommended)
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

### Running the Application

#### Development Mode

1. **Start the Whisper API service:**
   ```bash
   cd whisper-api
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   python whisper_api.py
   ```

2. **Start the WebSocket server:**
   ```bash
   cd server
   npm run dev
   ```

3. **Start the React client:**
   ```bash
   cd client
   npm start
   ```

#### Production Mode with Docker

```bash
# Build and start all services
docker-compose up --build

# Or run in background
docker-compose up -d --build
```

## Features

- Real-time audio recording from microphone
- WebSocket-based communication for low latency
- OpenAI Whisper integration for accurate transcription
- TypeScript support for better development experience
- Responsive React frontend

## API Endpoints

### WebSocket Server (Port 8080)
- **WebSocket**: `ws://localhost:8080`
- **Health Check**: `http://localhost:8080/health`
- **Client Info**: `http://localhost:8080/clients`

### Whisper API (Port 8000)
- **Transcription**: `POST http://localhost:8000/transcribe`
- **Health Check**: `http://localhost:8000/health`
- **Available Models**: `http://localhost:8000/models`
- **Change Model**: `POST http://localhost:8000/change-model`

### React Client (Port 3000)
- **Application**: `http://localhost:3000`

## Configuration

### Whisper Model Options
The system supports different Whisper model sizes:
- **tiny**: Fastest, least accurate (~39 MB)
- **base**: Good balance of speed and accuracy (~74 MB) - Default
- **small**: Better accuracy, slower (~244 MB)
- **medium**: High accuracy, slower (~769 MB)
- **large**: Best accuracy, slowest (~1550 MB)

### Environment Variables

#### Server (.env)
```bash
PORT=8080
NODE_ENV=development
```

#### Whisper API (.env)
```bash
HOST=0.0.0.0
PORT=8000
MODEL_SIZE=base
```

#### Client (.env)
```bash
REACT_APP_WS_URL=ws://localhost:8080
REACT_APP_API_URL=http://localhost:8000
```

## Troubleshooting

### Common Issues

1. **Microphone Access Denied**
   - Ensure your browser has microphone permissions
   - Use HTTPS in production for microphone access

2. **WebSocket Connection Failed**
   - Check if the WebSocket server is running on port 8080
   - Verify firewall settings

3. **Whisper API Not Responding**
   - Ensure Python dependencies are installed
   - Check if port 8000 is available
   - Verify FFmpeg is installed for audio processing

4. **Audio Quality Issues**
   - Try different Whisper model sizes
   - Check microphone quality and positioning
   - Reduce background noise

### Performance Tips

- Use GPU acceleration for Whisper if available (CUDA)
- Choose appropriate model size based on accuracy vs speed requirements
- Consider chunking long audio for better real-time performance

## Development

### Project Structure Details

```
real-time-transcription/
├── client/                     # React TypeScript frontend
│   ├── public/                # Static assets
│   ├── src/
│   │   ├── App.tsx           # Main application component
│   │   ├── App.css           # Application styles
│   │   ├── index.tsx         # React entry point
│   │   └── index.css         # Global styles
│   ├── package.json          # Client dependencies
│   ├── tsconfig.json         # TypeScript configuration
│   ├── Dockerfile            # Client container
│   └── nginx.conf            # Nginx configuration for production
├── server/                     # Node.js WebSocket server
│   ├── src/
│   │   └── index.ts          # Server entry point
│   ├── package.json          # Server dependencies
│   ├── tsconfig.json         # TypeScript configuration
│   └── Dockerfile            # Server container
├── whisper-api/               # FastAPI Whisper service
│   ├── whisper_api.py        # FastAPI application
│   ├── requirements.txt      # Python dependencies
│   └── Dockerfile            # API container
├── scripts/                   # Development scripts
│   ├── start-dev.sh          # Linux/macOS startup script
│   └── start-dev.bat         # Windows startup script
├── docker-compose.yml        # Docker orchestration
├── package.json              # Root package.json for workspace
├── .gitignore               # Git ignore rules
└── README.md                # This file
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License
