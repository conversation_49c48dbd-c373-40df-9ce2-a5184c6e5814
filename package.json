{"name": "real-time-transcription", "version": "1.0.0", "description": "Real-time audio transcription system with React, Node.js WebSocket server, and FastAPI Whisper service", "private": true, "scripts": {"install:all": "npm run install:client && npm run install:server", "install:client": "cd client && npm install", "install:server": "cd server && npm install", "install:whisper": "cd whisper-api && pip install -r requirements.txt", "dev": "concurrently \"npm run dev:whisper\" \"npm run dev:server\" \"npm run dev:client\"", "dev:client": "cd client && npm start", "dev:server": "cd server && npm run dev", "dev:whisper": "cd whisper-api && python whisper_api.py", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "start": "concurrently \"npm run start:whisper\" \"npm run start:server\" \"npm run start:client\"", "start:client": "cd client && serve -s build -l 3000", "start:server": "cd server && npm start", "start:whisper": "cd whisper-api && python whisper_api.py", "test": "npm run test:client && npm run test:server", "test:client": "cd client && npm test -- --coverage --watchAll=false", "test:server": "cd server && npm test", "lint": "npm run lint:client && npm run lint:server", "lint:client": "cd client && npm run lint", "lint:server": "cd server && npm run lint", "clean": "npm run clean:client && npm run clean:server", "clean:client": "cd client && rm -rf build node_modules", "clean:server": "cd server && rm -rf dist node_modules", "docker:build": "docker-compose build", "docker:up": "docker-compose up", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "keywords": ["transcription", "real-time", "audio", "whisper", "websocket", "react", "typescript", "<PERSON><PERSON><PERSON>", "nodejs"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.0", "serve": "^14.2.1"}, "workspaces": ["client", "server"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"autoprefixer": "^10.4.21", "browserslist": "^4.25.1", "caniuse-lite": "^1.0.30001726", "core-js-pure": "^3.43.0", "lodash": "^4.17.21", "postcss": "^8.5.6", "postcss-preset-env": "^10.2.4", "resolve": "^1.22.10", "semver": "^7.7.2", "workbox-build": "^7.3.0", "workbox-webpack-plugin": "^7.3.0"}}