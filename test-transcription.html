<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Real-Time Transcription Test</title>
    <style>
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: white;
      }
      .container {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
      }
      h1 {
        text-align: center;
        margin-bottom: 30px;
        font-size: 2.5em;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      }
      .controls {
        text-align: center;
        margin-bottom: 30px;
      }
      button {
        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        border: none;
        color: white;
        padding: 15px 30px;
        font-size: 18px;
        border-radius: 50px;
        cursor: pointer;
        margin: 0 10px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
      }
      button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.3);
      }
      button:disabled {
        background: #666;
        cursor: not-allowed;
        transform: none;
      }
      .status {
        text-align: center;
        margin: 20px 0;
        font-size: 18px;
        font-weight: bold;
      }
      .transcription {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        padding: 20px;
        min-height: 200px;
        font-size: 16px;
        line-height: 1.6;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }
      .word-display {
        margin-top: 20px;
      }
      .word-display h3 {
        color: #2c3e50;
        margin-bottom: 10px;
        font-size: 18px;
        text-align: center;
      }
      .word-display-area {
        background: #2c3e50;
        color: #ecf0f1;
        border-radius: 15px;
        padding: 20px;
        min-height: 100px;
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        font-family: 'Courier New', monospace;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      }
      .word {
        display: inline-block;
        margin: 2px 4px;
        padding: 4px 8px;
        background: #3498db;
        border-radius: 4px;
        animation: wordAppear 0.3s ease-in;
        transition: all 0.3s ease;
      }
      .word.current {
        background: #e74c3c;
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(231, 76, 60, 0.4);
      }
      @keyframes wordAppear {
        from {
          opacity: 0;
          transform: translateY(-10px) scale(0.8);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }
      .connection-status {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: bold;
        font-size: 14px;
      }
      .connected {
        background: #27ae60;
        color: white;
      }
      .disconnected {
        background: #e74c3c;
        color: white;
      }
      .recording {
        background: linear-gradient(45deg, #27ae60, #2ecc71) !important;
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
      }
    </style>
  </head>
  <body>
    <div class="connection-status" id="connectionStatus">Disconnected</div>
    <div class="container">
      <h1>🎤 Real-Time Transcription</h1>
      
      <div class="controls">
        <button id="startBtn">Start Recording</button>
        <button id="stopBtn" disabled>Stop Recording</button>
        <button id="clearBtn">Clear</button>
        <br><br>
        <input type="file" id="fileInput" accept=".wav,.mp3,.m4a,.ogg,.flac" style="display: none;">
        <button id="uploadBtn">📁 Upload WAV File</button>
      </div>
      
      <div class="status" id="status">Click "Start Recording" to begin or upload a WAV file for best quality</div>
      
      <div class="transcription" id="transcription">
        Your transcribed text will appear here...
      </div>

      <div class="word-display">
        <h3>🎯 Real-time Words (Live Subtitles)</h3>
        <div class="word-display-area" id="wordDisplay">
          Words will appear here in real-time like subtitles...
        </div>
      </div>
    </div>

    <script>
      class RealTimeTranscription {
        constructor() {
          this.ws = null;
          this.mediaRecorder = null;
          this.audioStream = null;
          this.isRecording = false;
          
          this.startBtn = document.getElementById('startBtn');
          this.stopBtn = document.getElementById('stopBtn');
          this.clearBtn = document.getElementById('clearBtn');
          this.uploadBtn = document.getElementById('uploadBtn');
          this.fileInput = document.getElementById('fileInput');
          this.status = document.getElementById('status');
          this.transcription = document.getElementById('transcription');
          this.wordDisplay = document.getElementById('wordDisplay');
          this.connectionStatus = document.getElementById('connectionStatus');
          
          this.initEventListeners();
          this.connectWebSocket();
        }
        
        initEventListeners() {
          this.startBtn.addEventListener('click', () => this.startRecording());
          this.stopBtn.addEventListener('click', () => this.stopRecording());
          this.clearBtn.addEventListener('click', () => this.clearTranscription());
          this.uploadBtn.addEventListener('click', () => this.fileInput.click());
          this.fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        }
        
        connectWebSocket() {
          try {
            this.ws = new WebSocket('ws://localhost:8080');
            
            this.ws.onopen = () => {
              console.log('WebSocket connected');
              this.updateConnectionStatus(true);
            };
            
            this.ws.onmessage = (event) => {
              const data = JSON.parse(event.data);
              if (data.type === 'transcription' && data.text) {
                this.appendTranscription(data.text);

                // Handle word-level data for real-time display
                if (data.words && Array.isArray(data.words)) {
                  this.displayWordsRealTime(data.words);
                }
              }
            };
            
            this.ws.onclose = () => {
              console.log('WebSocket disconnected');
              this.updateConnectionStatus(false);
              // Attempt to reconnect after 3 seconds
              setTimeout(() => this.connectWebSocket(), 3000);
            };
            
            this.ws.onerror = (error) => {
              console.error('WebSocket error:', error);
              this.updateConnectionStatus(false);
            };
          } catch (error) {
            console.error('Failed to connect WebSocket:', error);
            this.updateConnectionStatus(false);
          }
        }
        
        updateConnectionStatus(connected) {
          this.connectionStatus.textContent = connected ? 'Connected' : 'Disconnected';
          this.connectionStatus.className = `connection-status ${connected ? 'connected' : 'disconnected'}`;
        }
        
        async startRecording() {
          try {
            // Send signal to clear previous transcription context
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
              console.log('🧹 Sending clear context signal for new recording session');
              this.ws.send(JSON.stringify({ type: 'clear_context' }));
            }

            this.audioStream = await navigator.mediaDevices.getUserMedia({
              audio: {
                sampleRate: 16000,  // 16kHz for optimal Whisper performance
                channelCount: 1,    // Mono audio
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true,
                // Optimize for transcription quality
                sampleSize: 16,     // 16-bit samples
                latency: 0.1,       // Low latency for real-time
                // Additional quality settings
                volume: 1.0,        // Full volume
                googEchoCancellation: true,
                googNoiseSuppression: true,
                googAutoGainControl: true,
                googHighpassFilter: false  // Keep low frequencies for speech
              }
            });

            // Try different audio formats in order of preference for transcription
            let mimeType = '';
            const preferredFormats = [
              'audio/wav',
              'audio/webm;codecs=opus',  // High quality WebM
              'audio/webm;codecs=pcm',   // PCM WebM (closer to WAV)
              'audio/webm',              // Standard WebM
              'audio/mp4',               // MP4 fallback
              ''                         // Browser default
            ];

            for (const format of preferredFormats) {
              if (format === '' || MediaRecorder.isTypeSupported(format)) {
                mimeType = format;
                break;
              }
            }

            // Log format selection for debugging
            if (mimeType.includes('wav')) {
              console.log('✅ Using WAV format - optimal for transcription');
            } else if (mimeType.includes('opus')) {
              console.log('📊 Using WebM with Opus codec - good quality for transcription');
            } else {
              console.log('⚠️ Using fallback format:', mimeType || 'browser default');
            }

            console.log('Using MIME type:', mimeType);
            console.log('WAV supported:', MediaRecorder.isTypeSupported('audio/wav'));
            console.log('WebM supported:', MediaRecorder.isTypeSupported('audio/webm'));

            // Configure MediaRecorder with optimal settings for transcription
            const recorderOptions = {
              mimeType: mimeType
            };

            // Set bitrate based on format for optimal transcription quality
            if (mimeType.includes('wav')) {
              recorderOptions.audioBitsPerSecond = 256000;  // High quality for WAV
            } else if (mimeType.includes('opus')) {
              recorderOptions.audioBitsPerSecond = 128000;  // Higher quality for Opus
            } else {
              recorderOptions.audioBitsPerSecond = 96000;   // Good quality for other formats
            }

            this.mediaRecorder = new MediaRecorder(this.audioStream, recorderOptions);

            this.mediaRecorder.ondataavailable = (event) => {
              if (event.data.size > 0 && this.ws && this.ws.readyState === WebSocket.OPEN) {
                // Send all chunks - let server handle accumulation
                console.log(`📤 Sending ${mimeType} audio chunk: ${event.data.size} bytes`);
                this.ws.send(event.data);
              }
            };

            // Use shorter intervals for better continuous transcription
            const recordingInterval = 2000; // 2 seconds for larger chunks and better paragraph handling
            this.mediaRecorder.start(recordingInterval);
            console.log(`🎤 Started recording with ${recordingInterval}ms intervals using ${mimeType}`);

            this.isRecording = true;
            
            this.startBtn.disabled = true;
            this.startBtn.classList.add('recording');
            this.stopBtn.disabled = false;
            this.status.textContent = '🔴 Recording... Speak now!';
            
          } catch (error) {
            console.error('Error starting recording:', error);
            this.status.textContent = 'Error: Could not access microphone';
          }
        }
        
        stopRecording() {
          if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.audioStream.getTracks().forEach(track => track.stop());
            this.isRecording = false;

            // Send flush signal to server to process any remaining buffer
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
              console.log('📤 Sending flush signal to process remaining buffer');
              this.ws.send(JSON.stringify({ type: 'flush_buffer' }));
            }

            this.startBtn.disabled = false;
            this.startBtn.classList.remove('recording');
            this.stopBtn.disabled = true;
            this.status.textContent = 'Recording stopped. Processing remaining audio...';

            // Update status after a delay to show completion
            setTimeout(() => {
              if (!this.isRecording) {
                this.status.textContent = 'Recording stopped. Click "Start Recording" to begin again.';
              }
            }, 3000);
          }
        }
        
        appendTranscription(text) {
          const currentText = this.transcription.textContent;
          if (currentText === 'Your transcribed text will appear here...') {
            this.transcription.textContent = text;
          } else {
            this.transcription.textContent = currentText + ' ' + text;
          }
          this.transcription.scrollTop = this.transcription.scrollHeight;
        }

        displayWordsRealTime(words) {
          // Clear previous words
          this.wordDisplay.innerHTML = '';

          if (!words || words.length === 0) {
            this.wordDisplay.textContent = 'Words will appear here in real-time like subtitles...';
            return;
          }

          // Display each word with timing
          words.forEach((wordData, index) => {
            const wordSpan = document.createElement('span');
            wordSpan.className = 'word';
            wordSpan.textContent = wordData.word || wordData;

            // Add timing information as tooltip
            if (wordData.start !== undefined && wordData.end !== undefined) {
              wordSpan.title = `${wordData.start.toFixed(2)}s - ${wordData.end.toFixed(2)}s`;
            }

            this.wordDisplay.appendChild(wordSpan);

            // Animate word appearance with delay
            setTimeout(() => {
              wordSpan.classList.add('current');

              // Remove current class after a short time
              setTimeout(() => {
                wordSpan.classList.remove('current');
              }, 500);
            }, index * 100); // Stagger the animation
          });

          // Auto-scroll to show latest words
          this.wordDisplay.scrollTop = this.wordDisplay.scrollHeight;
        }

        async handleFileUpload(event) {
          const file = event.target.files[0];
          if (!file) return;

          // Check file type
          const allowedTypes = ['audio/wav', 'audio/mpeg', 'audio/mp4', 'audio/ogg', 'audio/flac'];
          if (!allowedTypes.includes(file.type) && !file.name.match(/\.(wav|mp3|m4a|ogg|flac)$/i)) {
            alert('Please select a valid audio file (.wav, .mp3, .m4a, .ogg, .flac)');
            return;
          }

          this.status.textContent = `📁 Processing file: ${file.name}...`;

          try {
            // Read file as array buffer
            const arrayBuffer = await file.arrayBuffer();
            const audioBuffer = new Uint8Array(arrayBuffer);

            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
              // Send file data via WebSocket
              this.ws.send(audioBuffer);
              this.status.textContent = `✅ File uploaded: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
            } else {
              this.status.textContent = '❌ WebSocket not connected. Please refresh and try again.';
            }
          } catch (error) {
            console.error('Error uploading file:', error);
            this.status.textContent = '❌ Error uploading file. Please try again.';
          }

          // Clear the file input
          event.target.value = '';
        }

        clearTranscription() {
          this.transcription.textContent = 'Your transcribed text will appear here...';
          this.wordDisplay.textContent = 'Words will appear here in real-time like subtitles...';
        }
      }
      
      // Initialize the application when the page loads
      document.addEventListener('DOMContentLoaded', () => {
        new RealTimeTranscription();
      });
    </script>
  </body>
</html>
