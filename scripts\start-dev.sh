#!/bin/bash

# Real-Time Transcription Development Startup Script

set -e

echo "🚀 Starting Real-Time Transcription Development Environment"
echo "============================================================"

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command_exists node; then
    echo "❌ Node.js is not installed. Please install Node.js 16 or higher."
    exit 1
fi

if ! command_exists python3; then
    echo "❌ Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

if ! command_exists npm; then
    echo "❌ npm is not installed. Please install npm."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Check if ports are available
echo "🔍 Checking port availability..."

if port_in_use 3000; then
    echo "⚠️  Port 3000 is already in use. Please stop the service using this port."
    exit 1
fi

if port_in_use 8080; then
    echo "⚠️  Port 8080 is already in use. Please stop the service using this port."
    exit 1
fi

if port_in_use 8000; then
    echo "⚠️  Port 8000 is already in use. Please stop the service using this port."
    exit 1
fi

echo "✅ All ports are available"

# Install dependencies
echo "📦 Installing dependencies..."

# Install client dependencies
echo "Installing client dependencies..."
cd client
if [ ! -d "node_modules" ]; then
    npm install
else
    echo "Client dependencies already installed"
fi
cd ..

# Install server dependencies
echo "Installing server dependencies..."
cd server
if [ ! -d "node_modules" ]; then
    npm install
else
    echo "Server dependencies already installed"
fi
cd ..

# Install Python dependencies
echo "Installing Python dependencies..."
cd whisper-api
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

source venv/bin/activate
pip install -r requirements.txt
cd ..

echo "✅ Dependencies installed successfully"

# Start services
echo "🎬 Starting services..."

# Start Whisper API in background
echo "Starting Whisper API service..."
cd whisper-api
source venv/bin/activate
python whisper_api.py &
WHISPER_PID=$!
cd ..

# Wait for Whisper API to start
echo "Waiting for Whisper API to start..."
sleep 10

# Start WebSocket server in background
echo "Starting WebSocket server..."
cd server
npm run dev &
SERVER_PID=$!
cd ..

# Wait for WebSocket server to start
echo "Waiting for WebSocket server to start..."
sleep 5

# Start React client
echo "Starting React client..."
cd client
npm start &
CLIENT_PID=$!
cd ..

echo "🎉 All services started successfully!"
echo ""
echo "📱 Application URLs:"
echo "   React Client:    http://localhost:3000"
echo "   WebSocket Server: ws://localhost:8080"
echo "   Whisper API:     http://localhost:8000"
echo ""
echo "📊 Health Check URLs:"
echo "   Server Health:   http://localhost:8080/health"
echo "   Whisper Health:  http://localhost:8000/health"
echo ""
echo "🛑 To stop all services, press Ctrl+C"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping services..."
    kill $CLIENT_PID 2>/dev/null || true
    kill $SERVER_PID 2>/dev/null || true
    kill $WHISPER_PID 2>/dev/null || true
    echo "✅ All services stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for user to stop
wait
