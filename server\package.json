{"name": "real-time-transcription-server", "version": "1.0.0", "description": "WebSocket server for real-time audio transcription", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["websocket", "transcription", "real-time", "audio", "typescript"], "author": "", "license": "MIT", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "form-data": "^4.0.0", "node-fetch": "^2.6.7", "ws": "^8.13.0"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/node": "^20.4.5", "@types/node-fetch": "^2.6.4", "@types/ws": "^8.5.5", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "eslint": "^8.45.0", "jest": "^29.6.1", "rimraf": "^5.0.1", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.1.6"}, "engines": {"node": ">=16.0.0"}}