<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Audio Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .recording {
            background: #dc3545 !important;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background: #e9ecef;
        }
        .transcript {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            min-height: 50px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Simple Audio Test</h1>
        <p>This is a simplified test to check if we can record and send audio properly.</p>
        
        <div>
            <button id="startBtn">🎤 Start Recording</button>
            <button id="stopBtn" disabled>⏹️ Stop Recording</button>
        </div>
        
        <div id="status" class="status">Ready to record</div>
        <div id="transcript" class="transcript">Transcription will appear here...</div>
    </div>

    <script>
        class SimpleAudioTest {
            constructor() {
                this.startBtn = document.getElementById('startBtn');
                this.stopBtn = document.getElementById('stopBtn');
                this.status = document.getElementById('status');
                this.transcript = document.getElementById('transcript');
                
                this.mediaRecorder = null;
                this.audioStream = null;
                this.isRecording = false;
                this.ws = null;
                
                this.setupEventListeners();
                this.connectWebSocket();
            }
            
            setupEventListeners() {
                this.startBtn.addEventListener('click', () => this.startRecording());
                this.stopBtn.addEventListener('click', () => this.stopRecording());
            }
            
            connectWebSocket() {
                this.ws = new WebSocket('ws://localhost:8080');
                
                this.ws.onopen = () => {
                    console.log('WebSocket connected');
                    this.status.textContent = '✅ Connected to server - Ready to record';
                };
                
                this.ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        console.log('Received:', data);
                        
                        if (data.type === 'transcription') {
                            this.transcript.textContent = data.text || 'No transcription received';
                        } else if (data.type === 'error') {
                            this.transcript.textContent = `Error: ${data.message}`;
                        }
                    } catch (error) {
                        console.error('Error parsing message:', error);
                    }
                };
                
                this.ws.onclose = () => {
                    console.log('WebSocket disconnected');
                    this.status.textContent = '❌ Disconnected from server';
                };
                
                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    this.status.textContent = '❌ Connection error';
                };
            }
            
            async startRecording() {
                try {
                    this.audioStream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            sampleRate: 16000,
                            channelCount: 1,
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        }
                    });
                    
                    // Try different MIME types to find one that works
                    const mimeTypes = [
                        'audio/webm;codecs=opus',
                        'audio/webm',
                        'audio/mp4',
                        'audio/ogg;codecs=opus',
                        ''
                    ];
                    
                    let selectedMimeType = '';
                    for (const mimeType of mimeTypes) {
                        if (MediaRecorder.isTypeSupported(mimeType)) {
                            selectedMimeType = mimeType;
                            break;
                        }
                    }
                    
                    console.log('Using MIME type:', selectedMimeType);
                    this.status.textContent = `🎤 Recording with ${selectedMimeType || 'default'} format...`;
                    
                    this.mediaRecorder = new MediaRecorder(this.audioStream, {
                        mimeType: selectedMimeType,
                        audioBitsPerSecond: 64000
                    });
                    
                    this.mediaRecorder.ondataavailable = (event) => {
                        if (event.data.size > 0 && this.ws && this.ws.readyState === WebSocket.OPEN) {
                            console.log('Sending audio chunk:', event.data.size, 'bytes');
                            this.ws.send(event.data);
                        }
                    };
                    
                    // Record for 5 seconds then stop automatically
                    this.mediaRecorder.start();
                    this.isRecording = true;
                    
                    this.startBtn.disabled = true;
                    this.startBtn.classList.add('recording');
                    this.stopBtn.disabled = false;
                    
                    // Auto-stop after 5 seconds
                    setTimeout(() => {
                        if (this.isRecording) {
                            this.stopRecording();
                        }
                    }, 5000);
                    
                } catch (error) {
                    console.error('Error starting recording:', error);
                    this.status.textContent = 'Error: Could not access microphone';
                }
            }
            
            stopRecording() {
                if (this.mediaRecorder && this.isRecording) {
                    this.mediaRecorder.stop();
                    this.isRecording = false;
                    
                    this.audioStream.getTracks().forEach(track => track.stop());
                    
                    this.startBtn.disabled = false;
                    this.startBtn.classList.remove('recording');
                    this.stopBtn.disabled = true;
                    
                    this.status.textContent = '⏹️ Recording stopped - Processing...';
                }
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new SimpleAudioTest();
        });
    </script>
</body>
</html>
