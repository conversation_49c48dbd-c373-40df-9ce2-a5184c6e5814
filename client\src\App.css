.App {
  text-align: center;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.App-header h1 {
  margin: 0 0 10px 0;
  font-size: 2rem;
}

.connection-status {
  font-size: 1rem;
  font-weight: 500;
}

.App-main {
  flex: 1;
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.record-button {
  background-color: #4CAF50;
  border: none;
  color: white;
  padding: 15px 30px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 600;
}

.record-button:hover:not(:disabled) {
  background-color: #45a049;
  transform: translateY(-2px);
}

.record-button.recording {
  background-color: #f44336;
  animation: pulse 1.5s infinite;
}

.record-button.recording:hover {
  background-color: #da190b;
}

.record-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  transform: none;
}

.clear-button {
  background-color: #ff9800;
  border: none;
  color: white;
  padding: 15px 30px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 600;
}

.clear-button:hover:not(:disabled) {
  background-color: #e68900;
  transform: translateY(-2px);
}

.clear-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  transform: none;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(244, 67, 54, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0);
  }
}

.transcription-container {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: left;
}

.transcription-container h2 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #4CAF50;
  padding-bottom: 10px;
}

/* Continuous transcription display - main feature */
.continuous-transcription {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.continuous-text {
  color: white;
  font-size: 20px;
  line-height: 1.6;
  text-align: center;
  font-weight: 500;
  word-wrap: break-word;
  max-width: 100%;
}

.current-processing {
  color: #ffeb3b;
  font-style: italic;
  animation: fadeInOut 1.5s infinite;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

.recording-status {
  background-color: #ffebee;
  border: 2px solid #f44336;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 20px;
  color: #c62828;
  font-weight: 600;
  text-align: center;
  animation: recordingPulse 2s infinite;
}

@keyframes recordingPulse {
  0%, 100% { background-color: #ffebee; }
  50% { background-color: #ffcdd2; }
}

.transcription-history {
  margin-top: 25px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.transcription-history summary {
  background-color: #f5f5f5;
  padding: 15px;
  cursor: pointer;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
  transition: background-color 0.2s ease;
}

.transcription-history summary:hover {
  background-color: #eeeeee;
}

.transcription-history[open] summary {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.current-transcription {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 20px;
  font-style: italic;
  color: #856404;
}

.transcription-list {
  max-height: 400px;
  overflow-y: auto;
}

.no-transcriptions {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px 20px;
}

.transcription-item {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  transition: all 0.2s ease;
}

.transcription-item:hover {
  background-color: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.transcription-text {
  font-size: 16px;
  line-height: 1.5;
  color: #333;
  margin-bottom: 8px;
}

.transcription-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #666;
  border-top: 1px solid #dee2e6;
  padding-top: 8px;
}

.confidence {
  background-color: #4CAF50;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .App-main {
    padding: 15px;
  }
  
  .controls {
    flex-direction: column;
    align-items: center;
  }
  
  .record-button,
  .clear-button {
    width: 100%;
    max-width: 300px;
  }
  
  .transcription-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
