import React, { useState, useRef, useEffect } from 'react';
import './App.css';

interface TranscriptionResult {
  text: string;
  timestamp: number;
  confidence?: number;
}

const App: React.FC = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [transcriptions, setTranscriptions] = useState<TranscriptionResult[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [currentTranscription, setCurrentTranscription] = useState('');
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const websocketRef = useRef<WebSocket | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  useEffect(() => {
    // Initialize WebSocket connection
    const connectWebSocket = () => {
      const ws = new WebSocket('ws://localhost:8080');
      
      ws.onopen = () => {
        console.log('Connected to WebSocket server');
        setIsConnected(true);
      };
      
      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          if (data.type === 'transcription') {
            const newTranscription: TranscriptionResult = {
              text: data.text,
              timestamp: Date.now(),
              confidence: data.confidence
            };
            setTranscriptions(prev => [...prev, newTranscription]);
            setCurrentTranscription('');
          } else if (data.type === 'partial') {
            setCurrentTranscription(data.text);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
      
      ws.onclose = () => {
        console.log('WebSocket connection closed');
        setIsConnected(false);
        // Attempt to reconnect after 3 seconds
        setTimeout(connectWebSocket, 3000);
      };
      
      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setIsConnected(false);
      };
      
      websocketRef.current = ws;
    };

    connectWebSocket();

    return () => {
      if (websocketRef.current) {
        websocketRef.current.close();
      }
    };
  }, []);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        } 
      });
      
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        sendAudioForTranscription(audioBlob);
        audioChunksRef.current = [];
      };
      
      mediaRecorder.start(1000); // Collect data every second
      setIsRecording(true);
      
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Error accessing microphone. Please check permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
    }
  };

  const sendAudioForTranscription = (audioBlob: Blob) => {
    if (websocketRef.current && websocketRef.current.readyState === WebSocket.OPEN) {
      const reader = new FileReader();
      reader.onload = () => {
        const arrayBuffer = reader.result as ArrayBuffer;
        websocketRef.current?.send(arrayBuffer);
      };
      reader.readAsArrayBuffer(audioBlob);
    }
  };

  const clearTranscriptions = () => {
    setTranscriptions([]);
    setCurrentTranscription('');
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>Real-Time Transcription</h1>
        <div className="connection-status">
          Status: {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
        </div>
      </header>
      
      <main className="App-main">
        <div className="controls">
          <button
            className={`record-button ${isRecording ? 'recording' : ''}`}
            onClick={isRecording ? stopRecording : startRecording}
            disabled={!isConnected}
          >
            {isRecording ? '⏹️ Stop Recording' : '🎤 Start Recording'}
          </button>
          
          <button
            className="clear-button"
            onClick={clearTranscriptions}
            disabled={transcriptions.length === 0}
          >
            🗑️ Clear
          </button>
        </div>
        
        <div className="transcription-container">
          <h2>Transcription Results</h2>
          
          {currentTranscription && (
            <div className="current-transcription">
              <em>Processing: {currentTranscription}</em>
            </div>
          )}
          
          <div className="transcription-list">
            {transcriptions.length === 0 ? (
              <p className="no-transcriptions">No transcriptions yet. Start recording to begin!</p>
            ) : (
              transcriptions.map((transcription, index) => (
                <div key={index} className="transcription-item">
                  <div className="transcription-text">{transcription.text}</div>
                  <div className="transcription-meta">
                    {new Date(transcription.timestamp).toLocaleTimeString()}
                    {transcription.confidence && (
                      <span className="confidence">
                        Confidence: {(transcription.confidence * 100).toFixed(1)}%
                      </span>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default App;
