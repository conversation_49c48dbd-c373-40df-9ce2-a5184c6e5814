import React, { useState, useRef, useEffect } from 'react';
import './App.css';

interface TranscriptionResult {
  text: string;
  timestamp: number;
  confidence?: number;
}

const App: React.FC = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [transcriptions, setTranscriptions] = useState<TranscriptionResult[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [currentTranscription, setCurrentTranscription] = useState('');
  const [continuousText, setContinuousText] = useState(''); // For continuous display
  const [isProcessing, setIsProcessing] = useState(false);
  const [streamingWords, setStreamingWords] = useState<string[]>([]); // For word-by-word streaming

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const websocketRef = useRef<WebSocket | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  useEffect(() => {
    // Initialize WebSocket connection
    const connectWebSocket = () => {
      const ws = new WebSocket('ws://localhost:8080');
      
      ws.onopen = () => {
        console.log('Connected to WebSocket server');
        setIsConnected(true);
      };
      
      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.type === 'word') {
            // Handle individual word streaming for immediate display
            const word = data.word.trim();
            if (word) {
              setStreamingWords(prev => [...prev, word]);

              // Update continuous text immediately
              setContinuousText(prev => {
                const updated = prev + (prev ? ' ' : '') + word;
                return updated;
              });
            }
          } else if (data.type === 'transcription') {
            const newText = data.text.trim();
            if (newText) {
              // Finalize the transcription segment
              const newTranscription: TranscriptionResult = {
                text: newText,
                timestamp: Date.now(),
                confidence: data.confidence
              };
              setTranscriptions(prev => [...prev, newTranscription]);

              // Clear streaming words for next segment
              setStreamingWords([]);
            }
            setCurrentTranscription('');
            setIsProcessing(false);
          } else if (data.type === 'partial') {
            setCurrentTranscription(data.text);
          } else if (data.type === 'processing') {
            setIsProcessing(true);
            setCurrentTranscription('Processing...');
          } else if (data.type === 'buffering') {
            setIsProcessing(true);
            setCurrentTranscription(data.message);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
      
      ws.onclose = () => {
        console.log('WebSocket connection closed');
        setIsConnected(false);
        // Attempt to reconnect after 3 seconds
        setTimeout(connectWebSocket, 3000);
      };
      
      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setIsConnected(false);
      };
      
      websocketRef.current = ws;
    };

    connectWebSocket();

    return () => {
      if (websocketRef.current) {
        websocketRef.current.close();
      }
    };
  }, []);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        } 
      });
      
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        sendAudioForTranscription(audioBlob);
        audioChunksRef.current = [];
      };
      
      mediaRecorder.start(150); // Collect data every 150ms for ultra-fast real-time processing
      setIsRecording(true);
      setContinuousText(''); // Clear previous session
      setStreamingWords([]); // Clear streaming words
      
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Error accessing microphone. Please check permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
    }
  };

  const sendAudioForTranscription = (audioBlob: Blob) => {
    if (websocketRef.current && websocketRef.current.readyState === WebSocket.OPEN) {
      const reader = new FileReader();
      reader.onload = () => {
        const arrayBuffer = reader.result as ArrayBuffer;
        websocketRef.current?.send(arrayBuffer);
      };
      reader.readAsArrayBuffer(audioBlob);
    }
  };

  const clearTranscriptions = () => {
    setTranscriptions([]);
    setCurrentTranscription('');
    setContinuousText('');
    setStreamingWords([]);
    setIsProcessing(false);
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>Real-Time Transcription</h1>
        <div className="connection-status">
          Status: {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
        </div>
      </header>
      
      <main className="App-main">
        <div className="controls">
          <button
            className={`record-button ${isRecording ? 'recording' : ''}`}
            onClick={isRecording ? stopRecording : startRecording}
            disabled={!isConnected}
          >
            {isRecording ? '⏹️ Stop Recording' : '🎤 Start Recording'}
          </button>
          
          <button
            className="clear-button"
            onClick={clearTranscriptions}
            disabled={transcriptions.length === 0}
          >
            🗑️ Clear
          </button>
        </div>
        
        <div className="transcription-container">
          <h2>Live Transcription {isProcessing && <span className="processing-indicator">⚡</span>}</h2>

          {/* Continuous text display - main feature */}
          <div className="continuous-transcription">
            <div className="continuous-text">
              {continuousText || (isRecording ? 'Listening... Start speaking!' : 'Click "Start Recording" to begin transcription')}
              {streamingWords.length > 0 && (
                <span className="streaming-words">
                  {streamingWords.map((word, index) => (
                    <span key={index} className="streaming-word">
                      {word}
                    </span>
                  ))}
                </span>
              )}
              {currentTranscription && (
                <span className="current-processing">
                  {isProcessing ? ' [Processing...]' : ` ${currentTranscription}`}
                </span>
              )}
            </div>
          </div>

          {/* Status indicator */}
          {isRecording && (
            <div className="recording-status">
              🔴 Recording - Speak now! Click "Stop Recording" when finished.
            </div>
          )}

          {/* Transcription history (collapsible) */}
          <details className="transcription-history">
            <summary>Transcription History ({transcriptions.length} segments)</summary>
            <div className="transcription-list">
              {transcriptions.length === 0 ? (
                <p className="no-transcriptions">No transcriptions yet.</p>
              ) : (
                transcriptions.map((transcription, index) => (
                  <div key={index} className="transcription-item">
                    <div className="transcription-text">{transcription.text}</div>
                    <div className="transcription-meta">
                      {new Date(transcription.timestamp).toLocaleTimeString()}
                      {transcription.confidence && (
                        <span className="confidence">
                          Confidence: {(transcription.confidence * 100).toFixed(1)}%
                        </span>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </details>
        </div>
      </main>
    </div>
  );
};

export default App;
