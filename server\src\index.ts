import WebSocket from 'ws';
import http from 'http';
import express from 'express';
import cors from 'cors';
import FormData from 'form-data';
import fetch from 'node-fetch';
import path from 'path';

const app = express();
const server = http.createServer(app);

// Enable CORS for all routes
app.use(cors({
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true
}));

app.use(express.json());

// Serve static files from the root directory (where test-transcription.html is located)
app.use(express.static(path.join(__dirname, '../../')));

// WebSocket server
const wss = new WebSocket.Server({ 
  server,
  cors: {
    origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
    credentials: true
  }
});

interface ClientConnection {
  ws: WebSocket;
  id: string;
  isAlive: boolean;
}

const clients = new Map<string, ClientConnection>();

// Audio buffer management for better transcription
const clientAudioBuffers = new Map<string, Buffer>();
const clientBufferTimers = new Map<string, NodeJS.Timeout>();
const clientLastTranscriptions = new Map<string, string>(); // Store last transcription for context
const MIN_AUDIO_SIZE = 30000; // Minimum 30KB before transcription (1-2 chunks) for immediate response
const MAX_BUFFER_SIZE = 600000; // Maximum 600KB buffer size for longer speech
const BUFFER_TIMEOUT = 2000; // 2 seconds timeout for immediate real-time processing

// Function to calculate text similarity (simple Jaccard similarity)
function calculateSimilarity(text1: string, text2: string): number {
  if (!text1 || !text2) return 0;

  const words1 = new Set(text1.toLowerCase().split(/\s+/));
  const words2 = new Set(text2.toLowerCase().split(/\s+/));

  const intersection = new Set([...words1].filter(x => words2.has(x)));
  const union = new Set([...words1, ...words2]);

  return intersection.size / union.size;
}

// Generate unique client ID
function generateClientId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Process accumulated audio buffer for a client
async function processClientAudioBuffer(clientId: string, ws: WebSocket, force: boolean = false): Promise<void> {
  const buffer = clientAudioBuffers.get(clientId);
  if (!buffer || buffer.length === 0) return;

  // Only process if we have enough audio data or forced
  if (!force && buffer.length < MIN_AUDIO_SIZE) return;

  // Clear any pending timer
  const existingTimer = clientBufferTimers.get(clientId);
  if (existingTimer) {
    clearTimeout(existingTimer);
    clientBufferTimers.delete(clientId);
  }

  console.log(`🎵 Processing accumulated audio buffer for client ${clientId}: ${buffer.length} bytes`);

  try {
    // Send processing notification
    ws.send(JSON.stringify({
      type: 'processing',
      message: `Processing ${Math.round(buffer.length / 1000)}KB of audio...`
    }));

    // Send to Whisper API
    const transcriptionResult = await transcribeAudio(buffer);

    if (transcriptionResult === null) {
      ws.send(JSON.stringify({
        type: 'transcription',
        text: '',
        words: [],
        error: 'Connection failed after retries'
      }));
    } else {
      // Only send non-empty transcriptions
      if (transcriptionResult.text && transcriptionResult.text.trim().length > 0) {
        const newText = transcriptionResult.text.trim();
        const lastText = clientLastTranscriptions.get(clientId) || '';

        // Check if this is a duplicate or very similar to the last transcription
        const similarity = calculateSimilarity(newText, lastText);
        if (similarity > 0.9) { // Reduced threshold for more real-time updates
          console.log(`🔄 Skipping duplicate transcription for client ${clientId} (${Math.round(similarity * 100)}% similar)`);
          return;
        }

        // Send word-by-word for immediate display
        if (transcriptionResult.words && transcriptionResult.words.length > 0) {
          // Send each word individually for immediate display
          transcriptionResult.words.forEach((word: any, index: number) => {
            setTimeout(() => {
              ws.send(JSON.stringify({
                type: 'word',
                word: word.word,
                start: word.start,
                end: word.end,
                probability: word.probability,
                isLast: index === transcriptionResult.words.length - 1,
                timestamp: Date.now()
              }));
            }, index * 50); // Stagger words by 50ms for faster, more immediate flow
          });
        }

        // Also send complete transcription
        ws.send(JSON.stringify({
          type: 'transcription',
          text: newText,
          confidence: transcriptionResult.confidence || null,
          language: transcriptionResult.language || null,
          words: transcriptionResult.words || null,
          timestamp: Date.now()
        }));
        console.log(`✅ Transcription sent to client ${clientId}: "${newText}"`);

        // Store transcription for context in future processing
        clientLastTranscriptions.set(clientId, newText);
      } else {
        console.log(`⚠️ Empty transcription result for client ${clientId}, skipping`);
      }
    }

    // Clear the buffer after processing to prevent repetition
    // For flush operations, clear completely. For regular processing, keep small overlap for continuity
    if (force) {
      clientAudioBuffers.set(clientId, Buffer.alloc(0));
      console.log(`🧹 Buffer completely cleared for client ${clientId} (flush operation)`);
    } else {
      // Keep only the last 15% of the buffer for continuity, but prevent repetition
      const currentBuffer = clientAudioBuffers.get(clientId) || Buffer.alloc(0);
      const keepSize = Math.min(currentBuffer.length * 0.15, 25000); // Keep max 25KB for faster processing
      const newBuffer = currentBuffer.slice(-keepSize);
      clientAudioBuffers.set(clientId, newBuffer);
      console.log(`🧹 Buffer trimmed for client ${clientId}: kept ${newBuffer.length} bytes for continuity`);
    }
  } catch (error) {
    console.error('Error processing audio buffer:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Failed to process accumulated audio',
      error: error instanceof Error ? error.message : 'Unknown error'
    }));
  }
}

// Clear audio buffer in Whisper API
async function clearAudioBuffer(): Promise<void> {
  try {
    const response = await fetch('http://localhost:8000/clear-buffer', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      console.warn('Failed to clear audio buffer:', response.statusText);
    } else {
      console.log('Audio buffer cleared successfully');
    }
  } catch (error) {
    console.warn('Error clearing audio buffer:', error);
  }
}

// Detect audio format from buffer
function detectAudioFormat(buffer: Buffer): { filename: string; contentType: string } {
  // Check for WAV header (RIFF...WAVE)
  if (buffer.length >= 12 &&
      buffer.subarray(0, 4).toString() === 'RIFF' &&
      buffer.subarray(8, 12).toString() === 'WAVE') {
    return { filename: 'audio.wav', contentType: 'audio/wav' };
  }

  // Check for WebM header
  if (buffer.length >= 4 && buffer[0] === 0x1a && buffer[1] === 0x45 && buffer[2] === 0xdf && buffer[3] === 0xa3) {
    return { filename: 'audio.webm', contentType: 'audio/webm' };
  }

  // Default to WAV for better transcription compatibility
  return { filename: 'audio.wav', contentType: 'audio/wav' };
}

// Send transcription request to Whisper API
async function transcribeAudio(audioBuffer: Buffer): Promise<any> {
  try {
    const audioFormat = detectAudioFormat(audioBuffer);
    console.log(`Detected audio format: ${audioFormat.filename} (${audioFormat.contentType})`);

    const formData = new FormData();
    formData.append('audio', audioBuffer, {
      filename: audioFormat.filename,
      contentType: audioFormat.contentType
    });

    // Add retry logic for connection issues
    let response;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        response = await fetch('http://localhost:8000/transcribe', {
          method: 'POST',
          body: formData,
          headers: formData.getHeaders(),
          timeout: 15000  // 15 second timeout for fast Whisper tiny model processing
        });
        break; // Success, exit retry loop
      } catch (error: any) {
        retryCount++;
        console.error(`Transcription attempt ${retryCount} failed:`, error.code || error.message);

        if (retryCount >= maxRetries) {
          console.error('Max retries reached, returning null');
          return null; // Return null instead of trying to use ws
        }

        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
      }
    }

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error transcribing audio:', error);
    throw error;
  }
}

// WebSocket connection handler
wss.on('connection', async (ws: WebSocket, request) => {
  const clientId = generateClientId();
  const client: ClientConnection = {
    ws,
    id: clientId,
    isAlive: true
  };

  clients.set(clientId, client);
  console.log(`Client ${clientId} connected. Total clients: ${clients.size}`);

  // Clear audio buffer for fresh start
  await clearAudioBuffer();

  // Initialize client audio buffer
  clientAudioBuffers.set(clientId, Buffer.alloc(0));

  // Send welcome message
  ws.send(JSON.stringify({
    type: 'connection',
    message: 'Connected to transcription server',
    clientId: clientId
  }));

  // Handle incoming messages (audio data)
  ws.on('message', async (data: WebSocket.Data) => {
    try {
      // First, check if this is a Buffer (binary audio data)
      if (data instanceof Buffer) {
        // Check if this buffer contains JSON text (control message sent as binary)
        try {
          const textContent = data.toString('utf8');
          if (textContent.startsWith('{') && textContent.includes('"type"')) {
            // This is a JSON control message sent as binary, parse it
            const message = JSON.parse(textContent);

            if (message.type === 'flush_buffer') {
              console.log(`🔄 Flush buffer request from client ${clientId}`);
              const currentBuffer = clientAudioBuffers.get(clientId);
              if (currentBuffer && currentBuffer.length > 0) {
                console.log(`📤 Processing final buffer: ${currentBuffer.length} bytes`);
                await processClientAudioBuffer(clientId, ws, true);
              } else {
                console.log(`ℹ️ No buffer to flush for client ${clientId}`);
              }
              return;
            }

            if (message.type === 'clear_context') {
              console.log(`🧹 Clearing transcription context for client ${clientId}`);
              clientLastTranscriptions.delete(clientId);
              clientAudioBuffers.set(clientId, Buffer.alloc(0));
              console.log('Audio buffer cleared successfully');
              return;
            }

            // Handle other control messages
            return;
          }
        } catch (e) {
          // Not JSON, treat as audio data
        }

        // This is actual binary audio data
        console.log(`📥 Received audio chunk from client ${clientId}, size: ${data.length} bytes`);

        // Get current buffer for this client
        let currentBuffer = clientAudioBuffers.get(clientId) || Buffer.alloc(0);

        // Accumulate audio data
        currentBuffer = Buffer.concat([currentBuffer, data]);

        // Check if buffer is getting too large
        if (currentBuffer.length > MAX_BUFFER_SIZE) {
          console.log(`⚠️ Buffer size exceeded for client ${clientId}, processing now`);
          clientAudioBuffers.set(clientId, currentBuffer);
          await processClientAudioBuffer(clientId, ws, true);
          return;
        }

        // Update buffer
        clientAudioBuffers.set(clientId, currentBuffer);
        console.log(`📊 Buffer size for client ${clientId}: ${currentBuffer.length} bytes`);

        // Clear any existing timer for this client
        const existingTimer = clientBufferTimers.get(clientId);
        if (existingTimer) {
          clearTimeout(existingTimer);
        }

        // Process if we have enough audio data
        if (currentBuffer.length >= MIN_AUDIO_SIZE) {
          console.log(`🎵 Buffer size reached threshold, processing ${currentBuffer.length} bytes`);
          await processClientAudioBuffer(clientId, ws, false);
        } else {
          // Set a timer to process buffer after timeout
          const timer = setTimeout(async () => {
            console.log(`⏰ Buffer timeout reached, processing ${currentBuffer.length} bytes`);
            await processClientAudioBuffer(clientId, ws, true);
            clientBufferTimers.delete(clientId);
          }, BUFFER_TIMEOUT);

          clientBufferTimers.set(clientId, timer);

          // Send acknowledgment that we're accumulating
          ws.send(JSON.stringify({
            type: 'buffering',
            message: `Buffering audio... (${Math.round(currentBuffer.length / 1000)}KB/${Math.round(MIN_AUDIO_SIZE / 1000)}KB)`
          }));
        }
      } else {
        // Handle text messages (string format)
        try {
          const message = JSON.parse(data.toString());

          if (message.type === 'flush_buffer') {
            console.log(`🔄 Flush buffer request from client ${clientId}`);
            const currentBuffer = clientAudioBuffers.get(clientId);
            if (currentBuffer && currentBuffer.length > 0) {
              console.log(`📤 Processing final buffer: ${currentBuffer.length} bytes`);
              await processClientAudioBuffer(clientId, ws, true);
            } else {
              console.log(`ℹ️ No buffer to flush for client ${clientId}`);
            }
            return;
          }

          if (message.type === 'clear_context') {
            console.log(`🧹 Clearing transcription context for client ${clientId}`);
            clientLastTranscriptions.delete(clientId);
            clientAudioBuffers.set(clientId, Buffer.alloc(0));
            console.log('Audio buffer cleared successfully');
            return;
          }

          // Handle other message types
          switch (message.type) {
            case 'ping':
              ws.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
              break;
            default:
              console.log(`Unknown message type: ${message.type}`);
          }
        } catch (parseError) {
          console.error('Error parsing text message:', parseError);
        }
      }
    } catch (error) {
      console.error('Error handling message:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Server error processing request'
      }));
    }
  });

  // Handle pong responses for heartbeat
  ws.on('pong', () => {
    client.isAlive = true;
  });

  // Handle client disconnect
  ws.on('close', async (code, reason) => {
    console.log(`Client ${clientId} disconnected. Code: ${code}, Reason: ${reason}`);

    // Process any remaining audio buffer before cleanup
    const remainingBuffer = clientAudioBuffers.get(clientId);
    if (remainingBuffer && remainingBuffer.length > 0) {
      console.log(`🔄 Processing remaining audio buffer for disconnected client ${clientId}`);
      // Note: Can't send to disconnected client, but we can log the result
      try {
        const transcriptionResult = await transcribeAudio(remainingBuffer);
        if (transcriptionResult && transcriptionResult.text) {
          console.log(`📝 Final transcription for ${clientId}: "${transcriptionResult.text}"`);
        }
      } catch (error) {
        console.error('Error processing final buffer:', error);
      }
    }

    // Cleanup
    clients.delete(clientId);
    clientAudioBuffers.delete(clientId);
    clientLastTranscriptions.delete(clientId);

    // Clear any pending timer
    const existingTimer = clientBufferTimers.get(clientId);
    if (existingTimer) {
      clearTimeout(existingTimer);
      clientBufferTimers.delete(clientId);
    }

    console.log(`Total clients: ${clients.size}`);
  });

  // Handle WebSocket errors
  ws.on('error', (error) => {
    console.error(`WebSocket error for client ${clientId}:`, error);
    clients.delete(clientId);
    clientAudioBuffers.delete(clientId);
    clientLastTranscriptions.delete(clientId);

    // Clear any pending timer
    const existingTimer = clientBufferTimers.get(clientId);
    if (existingTimer) {
      clearTimeout(existingTimer);
      clientBufferTimers.delete(clientId);
    }
  });
});

// Heartbeat to detect broken connections
const heartbeat = setInterval(() => {
  wss.clients.forEach((ws) => {
    const client = Array.from(clients.values()).find(c => c.ws === ws);
    if (client) {
      if (!client.isAlive) {
        console.log(`Terminating inactive client ${client.id}`);
        clients.delete(client.id);
        ws.terminate();
        return;
      }
      client.isAlive = false;
      ws.ping();
    }
  });
}, 30000); // Check every 30 seconds

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    connectedClients: clients.size,
    uptime: process.uptime()
  });
});

// Get connected clients info
app.get('/clients', (req, res) => {
  const clientInfo = Array.from(clients.values()).map(client => ({
    id: client.id,
    isAlive: client.isAlive,
    readyState: client.ws.readyState
  }));
  
  res.json({
    totalClients: clients.size,
    clients: clientInfo
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  clearInterval(heartbeat);
  
  // Close all WebSocket connections
  clients.forEach((client) => {
    client.ws.close(1000, 'Server shutting down');
  });
  
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

const PORT = process.env.PORT || 8080;

server.listen(PORT, () => {
  console.log(`🚀 WebSocket server running on port ${PORT}`);
  console.log(`📊 Health check available at http://localhost:${PORT}/health`);
  console.log(`👥 Client info available at http://localhost:${PORT}/clients`);
});
