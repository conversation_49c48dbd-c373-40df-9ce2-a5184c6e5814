{"name": "real-time-transcription-client", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.11.56", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "fork-ts-checker-webpack-plugin": "^9.1.0", "lodash": "^4.17.21", "prompts": "^2.4.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "resolve": "^1.22.10", "semver": "^7.7.2", "typescript": "^4.7.4", "web-vitals": "^2.1.4", "webpack": "^5.99.9", "webpack-dev-server": "^5.2.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8080"}