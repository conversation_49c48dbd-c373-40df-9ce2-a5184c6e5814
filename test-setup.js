#!/usr/bin/env node

/**
 * Test script to verify the workspace setup
 */

const fs = require('fs');
const path = require('path');

const requiredFiles = [
  'README.md',
  'package.json',
  '.gitignore',
  'docker-compose.yml',
  
  // Client files
  'client/package.json',
  'client/tsconfig.json',
  'client/src/App.tsx',
  'client/src/index.tsx',
  'client/src/App.css',
  'client/src/index.css',
  'client/public/index.html',
  'client/Dockerfile',
  'client/nginx.conf',
  
  // Server files
  'server/package.json',
  'server/tsconfig.json',
  'server/src/index.ts',
  'server/Dockerfile',
  
  // Whisper API files
  'whisper-api/whisper_api.py',
  'whisper-api/requirements.txt',
  'whisper-api/Dockerfile',
  
  // Scripts
  'scripts/start-dev.sh',
  'scripts/start-dev.bat'
];

const requiredDirectories = [
  'client',
  'client/src',
  'client/public',
  'server',
  'server/src',
  'whisper-api',
  'scripts'
];

console.log('🔍 Testing Real-Time Transcription Workspace Setup');
console.log('==================================================');

let allTestsPassed = true;

// Test directories
console.log('\n📁 Checking directories...');
requiredDirectories.forEach(dir => {
  if (fs.existsSync(dir) && fs.statSync(dir).isDirectory()) {
    console.log(`✅ ${dir}`);
  } else {
    console.log(`❌ ${dir} - Missing directory`);
    allTestsPassed = false;
  }
});

// Test files
console.log('\n📄 Checking files...');
requiredFiles.forEach(file => {
  if (fs.existsSync(file) && fs.statSync(file).isFile()) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - Missing file`);
    allTestsPassed = false;
  }
});

// Test package.json files for required dependencies
console.log('\n📦 Checking package.json dependencies...');

// Check client package.json
try {
  const clientPackage = JSON.parse(fs.readFileSync('client/package.json', 'utf8'));
  const requiredClientDeps = ['react', 'react-dom', 'typescript', '@types/react'];
  
  requiredClientDeps.forEach(dep => {
    if (clientPackage.dependencies && clientPackage.dependencies[dep]) {
      console.log(`✅ Client: ${dep}`);
    } else if (clientPackage.devDependencies && clientPackage.devDependencies[dep]) {
      console.log(`✅ Client: ${dep} (dev)`);
    } else {
      console.log(`❌ Client: ${dep} - Missing dependency`);
      allTestsPassed = false;
    }
  });
} catch (error) {
  console.log(`❌ Error reading client/package.json: ${error.message}`);
  allTestsPassed = false;
}

// Check server package.json
try {
  const serverPackage = JSON.parse(fs.readFileSync('server/package.json', 'utf8'));
  const requiredServerDeps = ['ws', 'express', 'cors', 'typescript'];
  
  requiredServerDeps.forEach(dep => {
    if (serverPackage.dependencies && serverPackage.dependencies[dep]) {
      console.log(`✅ Server: ${dep}`);
    } else if (serverPackage.devDependencies && serverPackage.devDependencies[dep]) {
      console.log(`✅ Server: ${dep} (dev)`);
    } else {
      console.log(`❌ Server: ${dep} - Missing dependency`);
      allTestsPassed = false;
    }
  });
} catch (error) {
  console.log(`❌ Error reading server/package.json: ${error.message}`);
  allTestsPassed = false;
}

// Check Python requirements
console.log('\n🐍 Checking Python requirements...');
try {
  const requirements = fs.readFileSync('whisper-api/requirements.txt', 'utf8');
  const requiredPythonDeps = ['fastapi', 'uvicorn', 'openai-whisper', 'torch'];
  
  requiredPythonDeps.forEach(dep => {
    if (requirements.includes(dep)) {
      console.log(`✅ Python: ${dep}`);
    } else {
      console.log(`❌ Python: ${dep} - Missing dependency`);
      allTestsPassed = false;
    }
  });
} catch (error) {
  console.log(`❌ Error reading whisper-api/requirements.txt: ${error.message}`);
  allTestsPassed = false;
}

// Final result
console.log('\n' + '='.repeat(50));
if (allTestsPassed) {
  console.log('🎉 All tests passed! Workspace setup is complete.');
  console.log('\n📋 Next steps:');
  console.log('1. Run: npm run install:all');
  console.log('2. Run: npm run install:whisper');
  console.log('3. Start development: npm run dev');
  console.log('   Or use: ./scripts/start-dev.sh (Linux/macOS)');
  console.log('   Or use: scripts\\start-dev.bat (Windows)');
  process.exit(0);
} else {
  console.log('❌ Some tests failed. Please check the missing files/directories.');
  process.exit(1);
}
