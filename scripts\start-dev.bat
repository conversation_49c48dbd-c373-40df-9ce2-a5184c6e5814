@echo off
REM Real-Time Transcription Development Startup Script for Windows

echo 🚀 Starting Real-Time Transcription Development Environment
echo ============================================================

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 16 or higher.
    pause
    exit /b 1
)

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed. Please install Python 3.8 or higher.
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed

REM Install dependencies
echo 📦 Installing dependencies...

REM Install client dependencies
echo Installing client dependencies...
cd client
if not exist "node_modules" (
    npm install
) else (
    echo Client dependencies already installed
)
cd ..

REM Install server dependencies
echo Installing server dependencies...
cd server
if not exist "node_modules" (
    npm install
) else (
    echo Server dependencies already installed
)
cd ..

REM Install Python dependencies
echo Installing Python dependencies...
cd whisper-api
if not exist "venv" (
    echo Creating Python virtual environment...
    python -m venv venv
)

call venv\Scripts\activate.bat
pip install -r requirements.txt
cd ..

echo ✅ Dependencies installed successfully

REM Start services
echo 🎬 Starting services...

REM Start Whisper API
echo Starting Whisper API service...
cd whisper-api
call venv\Scripts\activate.bat
start "Whisper API" python whisper_api.py
cd ..

REM Wait for Whisper API to start
echo Waiting for Whisper API to start...
timeout /t 10 /nobreak >nul

REM Start WebSocket server
echo Starting WebSocket server...
cd server
start "WebSocket Server" npm run dev
cd ..

REM Wait for WebSocket server to start
echo Waiting for WebSocket server to start...
timeout /t 5 /nobreak >nul

REM Start React client
echo Starting React client...
cd client
start "React Client" npm start
cd ..

echo 🎉 All services started successfully!
echo.
echo 📱 Application URLs:
echo    React Client:    http://localhost:3000
echo    WebSocket Server: ws://localhost:8080
echo    Whisper API:     http://localhost:8000
echo.
echo 📊 Health Check URLs:
echo    Server Health:   http://localhost:8080/health
echo    Whisper Health:  http://localhost:8000/health
echo.
echo 🛑 To stop services, close the individual terminal windows
echo.
pause
