#!/usr/bin/env python3
"""
FastAPI service for audio transcription using OpenAI Whisper
"""

import os
import tempfile
import logging
from typing import Optional, Dict, Any
import uvicorn
import asyncio
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import whisper
import torch
from pydantic import BaseModel

# Configure logging first
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add FFmpeg to PATH for this process
ffmpeg_path = r"C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin"
if ffmpeg_path not in os.environ.get('PATH', ''):
    os.environ['PATH'] = ffmpeg_path + os.pathsep + os.environ.get('PATH', '')
    logger.info(f"Added FFmpeg to PATH: {ffmpeg_path}")

# Initialize FastAPI app
app = FastAPI(
    title="Whisper Transcription API",
    description="Real-time audio transcription service using OpenAI Whisper",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000", "http://127.0.0.1:8080"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Response models
class WordTimestamp(BaseModel):
    word: str
    start: float
    end: float
    probability: Optional[float] = None

class TranscriptionResponse(BaseModel):
    text: str
    language: Optional[str] = None
    confidence: Optional[float] = None
    processing_time: Optional[float] = None
    words: Optional[list[WordTimestamp]] = None

class HealthResponse(BaseModel):
    status: str
    model_loaded: bool
    device: str
    available_models: list

# Global variables
whisper_model = None
model_name = "tiny"  # Options: tiny, base, small, medium, large - using 'tiny' for maximum speed in real-time

# Global buffer for accumulating fragmented WebM audio data
audio_buffer = bytearray()
buffer_lock = asyncio.Lock()
device = "cuda" if torch.cuda.is_available() else "cpu"

def load_whisper_model(model_size: str = "base") -> whisper.Whisper:
    """Load Whisper model with specified size"""
    global whisper_model, model_name
    
    try:
        logger.info(f"Loading Whisper model '{model_size}' on device '{device}'...")
        model = whisper.load_model(model_size, device=device)
        model_name = model_size
        logger.info(f"Successfully loaded Whisper model '{model_size}'")
        return model
    except Exception as e:
        logger.error(f"Failed to load Whisper model: {e}")
        raise

def get_model():
    """Get the loaded Whisper model, load if not already loaded"""
    global whisper_model
    if whisper_model is None:
        whisper_model = load_whisper_model(model_name)
    return whisper_model

@app.on_event("startup")
async def startup_event():
    """Initialize the Whisper model on startup"""
    try:
        get_model()
        logger.info("Whisper API service started successfully")
    except Exception as e:
        logger.error(f"Failed to start Whisper API service: {e}")
        raise

@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint"""
    return {
        "message": "Whisper Transcription API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        model = get_model()
        model_loaded = model is not None
    except Exception:
        model_loaded = False
    
    return HealthResponse(
        status="healthy" if model_loaded else "unhealthy",
        model_loaded=model_loaded,
        device=device,
        available_models=["tiny", "base", "small", "medium", "large"]
    )

@app.post("/transcribe-buffered", response_model=TranscriptionResponse)
async def transcribe_audio_buffered(audio: UploadFile = File(...)):
    """
    Transcribe audio with buffering for fragmented WebM streams

    This endpoint accumulates audio chunks to handle MediaRecorder fragmentation
    where only the first chunk contains WebM headers.
    """
    global audio_buffer
    import time
    start_time = time.time()

    # Validate file
    if not audio.filename:
        raise HTTPException(status_code=400, detail="No file provided")

    # Read audio content
    audio_content = await audio.read()
    logger.info(f"Received audio chunk: {audio.filename} ({len(audio_content)} bytes)")

    if len(audio_content) == 0:
        raise HTTPException(status_code=400, detail="Empty file provided")

    try:
        async with buffer_lock:
            # Add new audio data to buffer
            audio_buffer.extend(audio_content)

            # Check if this looks like a complete WebM file (has proper header)
            is_complete_webm = (
                len(audio_buffer) > 100 and
                audio_buffer[:4] == b'\x1a\x45\xdf\xa3'  # WebM/Matroska signature
            )

            logger.info(f"Buffer size: {len(audio_buffer)} bytes, WebM header: {is_complete_webm}")

            # For small buffers, wait for more data unless we have a complete WebM
            if len(audio_buffer) < 15000 and not is_complete_webm:
                logger.info("Buffer too small, waiting for more data")
                return TranscriptionResponse(
                    text="",
                    language="en",
                    confidence=None,
                    processing_time=0.0,
                    words=[]
                )

            # Use the accumulated buffer for transcription
            buffer_to_process = bytes(audio_buffer)

            # Clear buffer after processing (keep some overlap for continuity)
            if len(audio_buffer) > 50000:  # Keep buffer from growing too large
                audio_buffer = audio_buffer[-5000:]  # Keep last 5KB for continuity
                logger.info(f"Buffer trimmed, new size: {len(audio_buffer)} bytes")

        # Get the Whisper model
        model = get_model()

        # Create temporary file
        file_extension = ".webm"
        if audio.filename and audio.filename.lower().endswith(('.mp3', '.wav', '.m4a', '.ogg', '.flac')):
            file_extension = "." + audio.filename.split('.')[-1].lower()

        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            temp_file.write(buffer_to_process)
            temp_file_path = temp_file.name

        try:
            logger.info(f"Processing accumulated buffer: {len(buffer_to_process)} bytes")

            # Transcribe with word-level timestamps - optimized for tiny model
            result = model.transcribe(
                temp_file_path,
                language='en',  # Force English to avoid mixed language issues
                task="transcribe",
                verbose=False,
                word_timestamps=True,  # Enable word-level timestamps for real-time display
                temperature=0.0,  # Use deterministic decoding for consistency
                # Note: tiny model doesn't support beam_size=1 or custom patience
                # Using default parameters for maximum compatibility
                suppress_tokens=[-1],  # Suppress silence tokens
                initial_prompt="This is clear English speech with complete sentences and paragraphs.",  # Help with longer speech
                condition_on_previous_text=True,  # Use context from previous text
                fp16=False,  # Use FP32 for better accuracy on CPU
                compression_ratio_threshold=2.4,  # Threshold for compression ratio
                logprob_threshold=-0.8,  # Better threshold for longer audio
                no_speech_threshold=0.3,  # Lower threshold for continuous speech detection
                no_speech_threshold=0.6  # Threshold for no speech detection
            )

            # Extract transcription details
            transcribed_text = result["text"].strip()
            detected_language = result.get("language", None)

            # Extract word-level timestamps
            words_data = []
            segments = result.get("segments", [])
            if segments:
                confidences = []
                for segment in segments:
                    # Extract word-level data from each segment
                    if "words" in segment:
                        for word_info in segment["words"]:
                            words_data.append(WordTimestamp(
                                word=word_info.get("word", "").strip(),
                                start=word_info.get("start", 0.0),
                                end=word_info.get("end", 0.0),
                                probability=word_info.get("probability", None)
                            ))

                    # Collect confidence data
                    if "avg_logprob" in segment:
                        confidences.append(segment["avg_logprob"])

                # Calculate average confidence
                if confidences:
                    avg_confidence = sum(confidences) / len(confidences)
                    confidence = max(0, min(1, (avg_confidence + 1) / 2))  # Normalize to 0-1
                else:
                    confidence = None
            else:
                confidence = None

            processing_time = time.time() - start_time
            logger.info(f"Buffered transcription completed in {processing_time:.2f}s: '{transcribed_text[:50]}...' with {len(words_data)} words")

            return TranscriptionResponse(
                text=transcribed_text,
                language=detected_language,
                confidence=confidence,
                processing_time=processing_time,
                words=words_data
            )

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file: {e}")

    except Exception as e:
        logger.error(f"Buffered transcription error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Buffered transcription failed: {str(e)}"
        )

@app.post("/transcribe", response_model=TranscriptionResponse)
async def transcribe_audio(audio: UploadFile = File(...)):
    """
    Transcribe audio file using Whisper
    
    Args:
        audio: Audio file (supports various formats: mp3, wav, m4a, webm, etc.)
    
    Returns:
        TranscriptionResponse with transcribed text and metadata
    """
    import time
    start_time = time.time()
    
    # Validate file
    if not audio.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    # Check file size (limit to 25MB)
    max_file_size = 25 * 1024 * 1024  # 25MB
    audio_content = await audio.read()
    
    if len(audio_content) > max_file_size:
        raise HTTPException(
            status_code=413, 
            detail=f"File too large. Maximum size is {max_file_size // (1024*1024)}MB"
        )
    
    if len(audio_content) == 0:
        raise HTTPException(status_code=400, detail="Empty file provided")
    
    try:
        # Get the Whisper model
        model = get_model()

        # Detect if this is a WebM fragment (missing header)
        is_webm_fragment = (
            len(audio_content) > 100 and
            not audio_content.startswith(b'\x1a\x45\xdf\xa3') and  # No WebM header
            (audio.filename and 'webm' in audio.filename.lower())
        )

        # Log audio format details for debugging
        logger.info(f"Audio analysis - Size: {len(audio_content)} bytes, Filename: {audio.filename}")
        logger.info(f"First 16 bytes: {audio_content[:16].hex() if len(audio_content) >= 16 else 'N/A'}")
        if len(audio_content) >= 4:
            # Check for common audio format signatures
            if audio_content.startswith(b'\x1a\x45\xdf\xa3'):
                logger.info("Format: WebM/EBML container detected")
            elif audio_content.startswith(b'OggS'):
                logger.info("Format: Ogg container detected")
            elif audio_content.startswith(b'RIFF'):
                logger.info("Format: WAV/RIFF container detected")
            elif audio_content.startswith(b'ftyp'):
                logger.info("Format: MP4 container detected")
            elif audio_content.startswith(b'OpusHead'):
                logger.info("Format: Raw Opus stream detected")
            else:
                logger.info("Format: Unknown/Raw audio data")

        if is_webm_fragment:
            logger.warning(f"Detected WebM fragment without header ({len(audio_content)} bytes) - attempting multiple conversion strategies")

            # Try multiple audio format conversions
            conversion_successful = False
            temp_file_path = None

            # Strategy 1: Try as Opus audio (common in WebM)
            try:
                with tempfile.NamedTemporaryFile(delete=False, suffix=".opus") as opus_file:
                    opus_file.write(audio_content)
                    opus_file_path = opus_file.name

                with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as wav_file:
                    wav_file_path = wav_file.name

                import subprocess
                ffmpeg_cmd = [
                    'ffmpeg', '-y', '-hide_banner', '-loglevel', 'error',
                    '-f', 'opus',
                    '-i', opus_file_path,
                    '-acodec', 'pcm_s16le',
                    '-ar', '16000',  # Whisper prefers 16kHz
                    wav_file_path
                ]

                result_ffmpeg = subprocess.run(ffmpeg_cmd, capture_output=True, text=True)
                if result_ffmpeg.returncode == 0:
                    temp_file_path = wav_file_path
                    conversion_successful = True
                    logger.info(f"Successfully converted WebM fragment as Opus to WAV")

                # Clean up opus file
                try:
                    os.unlink(opus_file_path)
                except:
                    pass

            except Exception as e:
                logger.debug(f"Opus conversion failed: {e}")

            # Strategy 2: Try as raw PCM if Opus failed
            if not conversion_successful:
                try:
                    with tempfile.NamedTemporaryFile(delete=False, suffix=".raw") as raw_file:
                        raw_file.write(audio_content)
                        raw_file_path = raw_file.name

                    with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as wav_file:
                        wav_file_path = wav_file.name

                    # Try different sample rates and formats
                    for sample_rate in ['48000', '44100', '16000']:
                        for format_type in ['s16le', 'f32le']:
                            for channels in ['1', '2']:
                                ffmpeg_cmd = [
                                    'ffmpeg', '-y', '-hide_banner', '-loglevel', 'error',
                                    '-f', format_type,
                                    '-ar', sample_rate,
                                    '-ac', channels,
                                    '-i', raw_file_path,
                                    '-acodec', 'pcm_s16le',
                                    '-ar', '16000',  # Whisper prefers 16kHz
                                    '-ac', '1',      # Convert to mono
                                    wav_file_path
                                ]

                                result_ffmpeg = subprocess.run(ffmpeg_cmd, capture_output=True, text=True)
                                if result_ffmpeg.returncode == 0:
                                    temp_file_path = wav_file_path
                                    conversion_successful = True
                                    logger.info(f"Successfully converted raw audio fragment to WAV (format: {format_type}, rate: {sample_rate}, channels: {channels})")
                                    break
                            if conversion_successful:
                                break
                        if conversion_successful:
                            break

                    # Clean up raw file
                    try:
                        os.unlink(raw_file_path)
                    except:
                        pass

                except Exception as e:
                    logger.debug(f"Raw PCM conversion failed: {e}")

            # If all conversions failed, return empty response
            if not conversion_successful:
                logger.error(f"All audio conversion strategies failed for WebM fragment")
                return TranscriptionResponse(
                    text="",
                    language=None,
                    confidence=None,
                    processing_time=0.0,
                    words=[]
                )
        else:
            # Create temporary file with proper extension detection
            file_extension = ".webm"
            if audio.filename:
                if audio.filename.lower().endswith(('.mp3', '.wav', '.m4a', '.ogg', '.flac')):
                    file_extension = "." + audio.filename.split('.')[-1].lower()

            with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
                temp_file.write(audio_content)
                temp_file_path = temp_file.name

        try:
            logger.info(f"Transcribing audio file: {audio.filename} ({len(audio_content)} bytes)")

            # Transcribe audio with parameters optimized for tiny model
            result = model.transcribe(
                temp_file_path,
                language='en',  # Force English to avoid mixed language issues
                task="transcribe",
                verbose=False,
                word_timestamps=True,  # Enable word-level timestamps for real-time display
                temperature=0.0,  # Use deterministic decoding for consistency
                # Note: tiny model doesn't support beam_size=1 or custom patience
                # Using default parameters for maximum compatibility
                suppress_tokens=[-1],  # Suppress silence tokens
                initial_prompt="This is clear English speech with complete sentences and paragraphs.",  # Help with longer speech
                condition_on_previous_text=False,  # Disable to prevent repetition from previous chunks
                fp16=False,  # Use FP32 for better accuracy on CPU
                compression_ratio_threshold=2.4,  # Threshold for compression ratio
                logprob_threshold=-0.8,  # Better threshold for longer audio
                no_speech_threshold=0.3  # Lower threshold for continuous speech detection
            )
            
            # Extract transcription details
            transcribed_text = result["text"].strip()
            detected_language = result.get("language", None)

            # Extract word-level timestamps
            words_data = []
            segments = result.get("segments", [])
            if segments:
                confidences = []
                for segment in segments:
                    # Extract word-level data from each segment
                    if "words" in segment:
                        for word_info in segment["words"]:
                            words_data.append(WordTimestamp(
                                word=word_info.get("word", "").strip(),
                                start=word_info.get("start", 0.0),
                                end=word_info.get("end", 0.0),
                                probability=word_info.get("probability", None)
                            ))

                    # Collect confidence data
                    if "avg_logprob" in segment:
                        confidences.append(segment["avg_logprob"])

                # Calculate average confidence
                if confidences:
                    avg_confidence = sum(confidences) / len(confidences)
                    confidence = max(0, min(1, (avg_confidence + 1) / 2))  # Normalize to 0-1
                else:
                    confidence = None
            else:
                confidence = None

            processing_time = time.time() - start_time

            logger.info(f"Transcription completed in {processing_time:.2f}s: '{transcribed_text[:100]}...' with {len(words_data)} words")

            return TranscriptionResponse(
                text=transcribed_text,
                language=detected_language,
                confidence=confidence,
                processing_time=processing_time,
                words=words_data
            )
            
        finally:
            # Clean up temporary file
            try:
                if 'temp_file_path' in locals():
                    os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file: {e}")
    
    except Exception as e:
        logger.error(f"Transcription error: {e}")
        # Return empty response instead of crashing to maintain server stability
        return TranscriptionResponse(
            text="",
            language=None,
            confidence=None,
            processing_time=0.0,
            words=[]
        )

@app.post("/clear-buffer")
async def clear_audio_buffer():
    """Clear the audio buffer for a fresh start"""
    global audio_buffer
    async with buffer_lock:
        buffer_size = len(audio_buffer)
        audio_buffer.clear()
        logger.info(f"Audio buffer cleared (was {buffer_size} bytes)")
    return {"message": f"Buffer cleared (was {buffer_size} bytes)", "status": "success"}

@app.post("/change-model")
async def change_model(model_size: str):
    """
    Change the Whisper model size
    
    Args:
        model_size: Model size (tiny, base, small, medium, large)
    """
    global whisper_model
    
    valid_models = ["tiny", "base", "small", "medium", "large"]
    if model_size not in valid_models:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid model size. Choose from: {valid_models}"
        )
    
    try:
        logger.info(f"Changing model to '{model_size}'...")
        whisper_model = load_whisper_model(model_size)
        return {
            "message": f"Successfully changed model to '{model_size}'",
            "model": model_size,
            "device": device
        }
    except Exception as e:
        logger.error(f"Failed to change model: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to change model: {str(e)}"
        )

@app.get("/models")
async def list_models():
    """List available Whisper models"""
    return {
        "available_models": ["tiny", "base", "small", "medium", "large"],
        "current_model": model_name,
        "device": device,
        "model_descriptions": {
            "tiny": "Fastest, least accurate (~39 MB)",
            "base": "Good balance of speed and accuracy (~74 MB)",
            "small": "Better accuracy, slower (~244 MB)",
            "medium": "High accuracy, slower (~769 MB)",
            "large": "Best accuracy, slowest (~1550 MB)"
        }
    }

if __name__ == "__main__":
    # Configuration
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    
    logger.info(f"Starting Whisper API server on {host}:{port}")
    logger.info(f"Using device: {device}")
    logger.info(f"Model: {model_name}")
    
    uvicorn.run(
        "whisper_api:app",
        host=host,
        port=port,
        reload=False,
        log_level="info"
    )
