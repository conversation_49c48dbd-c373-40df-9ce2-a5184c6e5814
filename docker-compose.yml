version: '3.8'

services:
  whisper-api:
    build:
      context: ./whisper-api
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
    volumes:
      - ./whisper-api:/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  websocket-server:
    build:
      context: ./server
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - NODE_ENV=production
    volumes:
      - ./server:/app
    depends_on:
      - whisper-api
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_WS_URL=ws://localhost:8080
      - REACT_APP_API_URL=http://localhost:8000
    volumes:
      - ./client:/app
      - /app/node_modules
    depends_on:
      - websocket-server
    restart: unless-stopped

networks:
  default:
    name: transcription-network
